import warnings
import os
import cv2
from pathlib import Path
import torch
import torch.nn as nn
import torch.nn.functional as F
import json
import time
import numpy as np
from PIL import Image, ImageDraw, ImageFont
import torchvision.transforms as transforms
from torchvision.models import resnet50, ResNet50_Weights

warnings.filterwarnings('ignore')
from ultralytics import YOLO

# 导入OCR相关库
try:
    import easyocr
    EASYOCR_AVAILABLE = True
except ImportError:
    EASYOCR_AVAILABLE = False
    print("EasyOCR not available. Please install: pip install easyocr")

try:
    from cnocr import CnOcr
    CNOCR_AVAILABLE = True
except ImportError:
    CNOCR_AVAILABLE = False
    print("CnOCR not available. Please install: pip install cnocr")

try:
    import pytesseract
    TESSERACT_AVAILABLE = True
except ImportError:
    TESSERACT_AVAILABLE = False
    print("Tesseract not available. Please install: pip install pytesseract")

class BackboneNetwork(nn.Module):
    """
    主干网络 - 使用ResNet50作为特征提取器
    """
    def __init__(self, pretrained=True):
        super(BackboneNetwork, self).__init__()

        # 加载预训练的ResNet50
        if pretrained:
            self.resnet = resnet50(weights=ResNet50_Weights.IMAGENET1K_V2)
        else:
            self.resnet = resnet50(weights=None)

        # 移除最后的全连接层和平均池化层
        self.features = nn.Sequential(*list(self.resnet.children())[:-2])

        # 获取不同层的特征图
        self.layer1 = nn.Sequential(*list(self.resnet.children())[:5])   # 256 channels
        self.layer2 = nn.Sequential(*list(self.resnet.children())[5:6])  # 512 channels
        self.layer3 = nn.Sequential(*list(self.resnet.children())[6:7])  # 1024 channels
        self.layer4 = nn.Sequential(*list(self.resnet.children())[7:8])  # 2048 channels

    def forward(self, x):
        """
        前向传播，返回多尺度特征图
        """
        # 逐层提取特征
        x = self.layer1(x)  # C2: [B, 256, H/4, W/4]
        c2 = x

        x = self.layer2(x)  # C3: [B, 512, H/8, W/8]
        c3 = x

        x = self.layer3(x)  # C4: [B, 1024, H/16, W/16]
        c4 = x

        x = self.layer4(x)  # C5: [B, 2048, H/32, W/32]
        c5 = x

        return c2, c3, c4, c5


class FeaturePyramidNetwork(nn.Module):
    """
    特征金字塔网络 (FPN) - 融合多尺度特征
    """
    def __init__(self, in_channels_list=[256, 512, 1024, 2048], out_channels=256):
        super(FeaturePyramidNetwork, self).__init__()

        self.out_channels = out_channels

        # 1x1卷积降维
        self.lateral_convs = nn.ModuleList()
        for in_channels in in_channels_list:
            self.lateral_convs.append(
                nn.Conv2d(in_channels, out_channels, kernel_size=1)
            )

        # 3x3卷积平滑特征
        self.fpn_convs = nn.ModuleList()
        for _ in range(len(in_channels_list)):
            self.fpn_convs.append(
                nn.Conv2d(out_channels, out_channels, kernel_size=3, padding=1)
            )

    def forward(self, features):
        """
        前向传播
        Args:
            features: [c2, c3, c4, c5] 多尺度特征图
        Returns:
            fpn_features: [p2, p3, p4, p5] FPN特征图
        """
        c2, c3, c4, c5 = features

        # 自顶向下路径
        # P5
        p5 = self.lateral_convs[3](c5)

        # P4
        p4 = self.lateral_convs[2](c4)
        p4 = p4 + F.interpolate(p5, size=p4.shape[-2:], mode='nearest')

        # P3
        p3 = self.lateral_convs[1](c3)
        p3 = p3 + F.interpolate(p4, size=p3.shape[-2:], mode='nearest')

        # P2
        p2 = self.lateral_convs[0](c2)
        p2 = p2 + F.interpolate(p3, size=p2.shape[-2:], mode='nearest')

        # 应用3x3卷积平滑
        p2 = self.fpn_convs[0](p2)
        p3 = self.fpn_convs[1](p3)
        p4 = self.fpn_convs[2](p4)
        p5 = self.fpn_convs[3](p5)

        return [p2, p3, p4, p5]

class ComponentDetectionHead(nn.Module):
    """
    元器件检测头 - 基于YOLO架构
    """
    def __init__(self, in_channels=256, num_classes=47, num_anchors=3):
        super(ComponentDetectionHead, self).__init__()

        self.num_classes = num_classes
        self.num_anchors = num_anchors

        # 检测头卷积层
        self.conv1 = nn.Conv2d(in_channels, in_channels, kernel_size=3, padding=1)
        self.conv2 = nn.Conv2d(in_channels, in_channels, kernel_size=3, padding=1)

        # 分类分支
        self.cls_head = nn.Conv2d(in_channels, num_anchors * num_classes, kernel_size=1)

        # 回归分支 (x, y, w, h)
        self.reg_head = nn.Conv2d(in_channels, num_anchors * 4, kernel_size=1)

        # 置信度分支
        self.obj_head = nn.Conv2d(in_channels, num_anchors, kernel_size=1)

        # 初始化权重
        self._init_weights()

    def _init_weights(self):
        """初始化权重"""
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.normal_(m.weight, std=0.01)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)

    def forward(self, features):
        """
        前向传播
        Args:
            features: [p2, p3, p4, p5] FPN特征图列表
        Returns:
            detections: 检测结果列表
        """
        detections = []

        for feature in features:
            # 共享卷积层
            x = F.relu(self.conv1(feature))
            x = F.relu(self.conv2(x))

            # 分类预测
            cls_pred = self.cls_head(x)  # [B, num_anchors*num_classes, H, W]

            # 边界框回归
            reg_pred = self.reg_head(x)  # [B, num_anchors*4, H, W]

            # 置信度预测
            obj_pred = torch.sigmoid(self.obj_head(x))  # [B, num_anchors, H, W]

            detections.append({
                'cls_pred': cls_pred,
                'reg_pred': reg_pred,
                'obj_pred': obj_pred
            })

        return detections


class TextDetectionHead(nn.Module):
    """
    文字检测+识别头 - 基于CRNN架构
    """
    def __init__(self, in_channels=256, num_chars=6000):  # 支持常用汉字+英文数字
        super(TextDetectionHead, self).__init__()

        self.num_chars = num_chars

        # 文字检测分支 - 类似DBNet
        self.text_det_conv1 = nn.Conv2d(in_channels, in_channels//2, kernel_size=3, padding=1)
        self.text_det_conv2 = nn.Conv2d(in_channels//2, in_channels//4, kernel_size=3, padding=1)
        self.text_det_head = nn.Conv2d(in_channels//4, 1, kernel_size=1)  # 二值化文字区域

        # 文字识别分支 - 类似CRNN
        self.text_rec_conv = nn.Conv2d(in_channels, 512, kernel_size=3, padding=1)

        # RNN层用于序列建模
        self.rnn = nn.LSTM(512, 256, num_layers=2, bidirectional=True, batch_first=True)

        # 字符分类头
        self.char_classifier = nn.Linear(512, num_chars + 1)  # +1 for blank token (CTC)

        # 初始化权重
        self._init_weights()

    def _init_weights(self):
        """初始化权重"""
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.Linear):
                nn.init.normal_(m.weight, std=0.01)
                nn.init.constant_(m.bias, 0)

    def forward(self, features):
        """
        前向传播
        Args:
            features: [p2, p3, p4, p5] FPN特征图列表
        Returns:
            text_results: 文字检测和识别结果
        """
        # 使用P2特征图进行文字检测（分辨率最高）
        p2_feature = features[0]  # [B, 256, H/4, W/4]

        # 文字检测分支
        det_x = F.relu(self.text_det_conv1(p2_feature))
        det_x = F.relu(self.text_det_conv2(det_x))
        text_map = torch.sigmoid(self.text_det_head(det_x))  # [B, 1, H/4, W/4]

        # 文字识别分支
        rec_x = F.relu(self.text_rec_conv(p2_feature))  # [B, 512, H/4, W/4]

        # 转换为序列格式用于RNN
        B, C, H, W = rec_x.shape
        rec_x = rec_x.permute(0, 3, 1, 2).contiguous()  # [B, W, C, H]
        rec_x = rec_x.view(B, W, C * H)  # [B, W, C*H] - 将高度维度展平

        # RNN处理序列
        rnn_out, _ = self.rnn(rec_x)  # [B, W, 512]

        # 字符分类
        char_pred = self.char_classifier(rnn_out)  # [B, W, num_chars+1]

        return {
            'text_map': text_map,      # 文字区域检测图
            'char_pred': char_pred     # 字符序列预测
        }



class MultiTaskModel(nn.Module):
    """
    真正的多任务模型架构：
    输入图像 -> 主干网络(Backbone) -> 特征金字塔(Neck)
    ├── 元器件检测头(YOLO head) -> 预测元器件类别和框
    └── 文字检测+识别头(OCR head) -> 预测文字框和字符内容
    """
    def __init__(self, num_classes: int = 47, num_chars: int = 6000, pretrained: bool = True):
        super(MultiTaskModel, self).__init__()

        self.num_classes = num_classes
        self.num_chars = num_chars

        # 1. 主干网络 (Backbone)
        self.backbone = BackboneNetwork(pretrained=pretrained)

        # 2. 特征金字塔网络 (Neck)
        self.fpn = FeaturePyramidNetwork(
            in_channels_list=[256, 512, 1024, 2048],
            out_channels=256
        )

        # 3. 元器件检测头 (YOLO Head)
        self.component_head = ComponentDetectionHead(
            in_channels=256,
            num_classes=num_classes,
            num_anchors=3
        )

        # 4. 文字检测+识别头 (OCR Head)
        self.text_head = TextDetectionHead(
            in_channels=256,
            num_chars=num_chars
        )

        # 图像预处理
        self.transform = transforms.Compose([
            transforms.ToPILImage(),
            transforms.Resize((640, 640)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])

        # 设置检测阈值
        self.detection_conf_threshold = 0.15
        self.ocr_confidence_threshold = 0.05

        # 初始化外部OCR引擎作为后备
        self.init_ocr_engines()



    def init_ocr_engines(self):
        """初始化OCR引擎"""
        self.ocr_engines = {}

        # EasyOCR引擎 - 支持中英文
        if EASYOCR_AVAILABLE:
            try:
                self.ocr_engines['easyocr'] = easyocr.Reader(['ch_sim', 'en'], gpu=torch.cuda.is_available())
                print("✓ EasyOCR引擎初始化成功")
            except Exception as e:
                print(f"✗ EasyOCR引擎初始化失败: {e}")

        # CnOCR引擎 - 高精度中文识别
        if CNOCR_AVAILABLE:
            try:
                self.ocr_engines['cnocr'] = CnOcr(
                    rec_model_name='densenet_lite_136-gru',
                    det_model_name='db_resnet18',
                    use_gpu=torch.cuda.is_available()
                )
                print("✓ CnOCR引擎初始化成功")
            except Exception as e:
                print(f"✗ CnOCR引擎初始化失败: {e}")

        # Tesseract引擎
        if TESSERACT_AVAILABLE:
            try:
                pytesseract.get_tesseract_version()
                self.ocr_engines['tesseract'] = pytesseract
                print("✓ Tesseract引擎初始化成功")
            except Exception as e:
                print(f"✗ Tesseract引擎初始化失败: {e}")

        print(f"📊 已初始化 {len(self.ocr_engines)} 个OCR引擎: {list(self.ocr_engines.keys())}")

    def forward(self, x):
        """
        前向传播 - 真正的多任务架构
        Args:
            x: 输入图像张量 [B, 3, H, W]
        Returns:
            dict: 包含元器件检测和文字检测结果
        """
        # 1. 主干网络特征提取
        backbone_features = self.backbone(x)  # [c2, c3, c4, c5]

        # 2. 特征金字塔网络
        fpn_features = self.fpn(backbone_features)  # [p2, p3, p4, p5]

        # 3. 并行执行两个检测头
        # 元器件检测分支
        component_detections = self.component_head(fpn_features)

        # 文字检测+识别分支
        text_results = self.text_head(fpn_features)

        return {
            'component_detections': component_detections,
            'text_results': text_results,
            'fpn_features': fpn_features
        }

    def preprocess_image(self, image):
        """
        预处理图像
        Args:
            image: OpenCV图像 (BGR格式)
        Returns:
            tensor: 预处理后的张量 [1, 3, H, W]
        """
        # 转换BGR到RGB
        if len(image.shape) == 3:
            image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        else:
            image_rgb = image

        # 应用变换
        tensor = self.transform(image_rgb)

        # 添加batch维度
        tensor = tensor.unsqueeze(0)

        return tensor

    def enhance_image_for_detection(self, image):
        """增强图像以提高检测精度"""
        try:
            # 转换为灰度图像进行处理
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image.copy()

            # 应用CLAHE (对比度限制自适应直方图均衡化)
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
            enhanced = clahe.apply(gray)

            # 转换回BGR格式
            if len(image.shape) == 3:
                enhanced = cv2.cvtColor(enhanced, cv2.COLOR_GRAY2BGR)

            return enhanced
        except Exception as e:
            print(f"图像增强失败: {e}")
            return image

    def detect_objects(self, image_path, conf_threshold=None):
        """使用YOLO进行目标检测"""
        if conf_threshold is None:
            conf_threshold = self.detection_conf_threshold

        print(f"🔍 开始YOLO目标检测，置信度阈值: {conf_threshold}")

        results = self.yolo_model.predict(
            image_path,
            conf=conf_threshold,
            device='0' if torch.cuda.is_available() else 'cpu',
            verbose=False
        )

        print(f"✅ YOLO检测完成")
        return results





    def extract_text_regions(self, image, detection_results):
        """从检测结果中提取可能包含文字的区域"""
        text_regions = []

        if len(detection_results) > 0:
            result = detection_results[0]
            if hasattr(result, 'boxes') and result.boxes is not None:
                for box in result.boxes:
                    x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                    confidence = float(box.conf[0])
                    class_id = int(box.cls[0])

                    # 扩展边界框以包含可能的文字区域
                    h, w = image.shape[:2]
                    margin = 10
                    x1 = max(0, int(x1) - margin)
                    y1 = max(0, int(y1) - margin)
                    x2 = min(w, int(x2) + margin)
                    y2 = min(h, int(y2) + margin)

                    region = image[y1:y2, x1:x2]
                    text_regions.append({
                        'region': region,
                        'bbox': (x1, y1, x2, y2),
                        'confidence': confidence,
                        'class_id': class_id
                    })

        return text_regions

    def detect_text_with_all_ocr(self, image):
        """使用所有OCR引擎进行文字检测和识别"""
        print("🔍 开始多引擎OCR检测...")

        all_text_regions = []

        # 使用CnOCR进行检测
        cnocr_regions = self.detect_text_with_cnocr(image)
        all_text_regions.extend(cnocr_regions)

        # 使用EasyOCR进行检测
        easyocr_regions = self.detect_text_with_easyocr(image)
        all_text_regions.extend(easyocr_regions)

        # 使用Tesseract进行检测
        tesseract_regions = self.detect_text_with_tesseract(image)
        all_text_regions.extend(tesseract_regions)

        # 合并和去重
        final_regions = self.ensemble_ocr_results(all_text_regions)
        print(f"✅ OCR检测完成，共检测到 {len(final_regions)} 个文字区域")

        return final_regions

    def detect_text_with_cnocr(self, image):
        """使用CnOCR进行文字检测和识别"""
        if 'cnocr' not in self.ocr_engines:
            return []

        try:
            if image is None or image.size == 0:
                return []

            # 增强图像
            enhanced_image = self.enhance_image_for_detection(image)

            # 转换为PIL图像
            if len(enhanced_image.shape) == 3:
                image_pil = Image.fromarray(cv2.cvtColor(enhanced_image, cv2.COLOR_BGR2RGB))
            else:
                image_pil = Image.fromarray(enhanced_image)

            # 使用CnOCR进行检测和识别
            results = self.ocr_engines['cnocr'].ocr(image_pil)

            text_regions = []
            if results:
                for result in results:
                    text = result.get('text', '')
                    confidence = result.get('score', 0.0)
                    position = result.get('position', [])

                    if confidence > self.ocr_confidence_threshold and text.strip():
                        if position and len(position) >= 4:
                            # 从position计算bbox
                            x_coords = [point[0] for point in position]
                            y_coords = [point[1] for point in position]
                            x1, x2 = min(x_coords), max(x_coords)
                            y1, y2 = min(y_coords), max(y_coords)
                            bbox = [int(x1), int(y1), int(x2), int(y2)]

                            text_regions.append({
                                'bbox': bbox,
                                'confidence': confidence,
                                'type': 'cnocr_detection',
                                'text': text,
                                'engine': 'cnocr'
                            })

            return text_regions
        except Exception as e:
            print(f"CnOCR检测错误: {e}")
            return []

    def detect_text_with_easyocr(self, image):
        """使用EasyOCR进行文字检测和识别"""
        if 'easyocr' not in self.ocr_engines:
            return []

        try:
            if image is None or image.size == 0:
                return []

            # 增强图像
            enhanced_image = self.enhance_image_for_detection(image)

            # EasyOCR检测
            results = self.ocr_engines['easyocr'].readtext(
                enhanced_image,
                width_ths=0.7,
                height_ths=0.7,
                text_threshold=0.7,
                low_text=0.4
            )

            text_regions = []
            for bbox, text, confidence in results:
                if confidence > self.ocr_confidence_threshold and text.strip():
                    # 转换bbox格式
                    x_coords = [point[0] for point in bbox]
                    y_coords = [point[1] for point in bbox]
                    x1, x2 = min(x_coords), max(x_coords)
                    y1, y2 = min(y_coords), max(y_coords)
                    bbox_rect = [int(x1), int(y1), int(x2), int(y2)]

                    text_regions.append({
                        'bbox': bbox_rect,
                        'confidence': confidence,
                        'type': 'easyocr_detection',
                        'text': text,
                        'engine': 'easyocr'
                    })

            return text_regions
        except Exception as e:
            print(f"EasyOCR检测错误: {e}")
            return []

    def detect_text_with_tesseract(self, image):
        """使用Tesseract进行文字检测和识别"""
        if 'tesseract' not in self.ocr_engines:
            return []

        try:
            if image is None or image.size == 0:
                return []

            # 增强图像
            enhanced_image = self.enhance_image_for_detection(image)

            # 配置Tesseract参数
            config = '--oem 3 --psm 6 -l chi_sim+eng'
            data = pytesseract.image_to_data(enhanced_image, config=config, output_type=pytesseract.Output.DICT)

            text_regions = []
            n_boxes = len(data['text'])

            for i in range(n_boxes):
                text_content = data['text'][i].strip()
                confidence = float(data['conf'][i]) / 100.0

                if confidence > self.ocr_confidence_threshold and text_content:
                    x, y, w, h = data['left'][i], data['top'][i], data['width'][i], data['height'][i]
                    bbox_rect = [int(x), int(y), int(x+w), int(y+h)]

                    text_regions.append({
                        'bbox': bbox_rect,
                        'confidence': confidence,
                        'type': 'tesseract_detection',
                        'text': text_content,
                        'engine': 'tesseract'
                    })

            return text_regions
        except Exception as e:
            print(f"Tesseract检测错误: {e}")
            return []



    def ensemble_ocr_results(self, all_ocr_results):
        """融合多个OCR引擎的结果以提高精度"""
        if not all_ocr_results:
            return []

        # 按置信度排序
        all_ocr_results.sort(key=lambda x: x['confidence'], reverse=True)

        # 去重和融合
        final_results = []
        for result in all_ocr_results:
            is_duplicate = False
            for existing in final_results:
                text_sim = self.text_similarity(result['text'], existing['text'])
                bbox_sim = self.bbox_similarity(result.get('bbox', []), existing.get('bbox', []))

                if text_sim > 0.8 or (text_sim > 0.5 and bbox_sim > 0.7):
                    is_duplicate = True
                    if result['confidence'] > existing['confidence']:
                        final_results.remove(existing)
                        final_results.append(result)
                    break

            if not is_duplicate:
                final_results.append(result)

        return sorted(final_results, key=lambda x: x['confidence'], reverse=True)

    def bbox_similarity(self, bbox1, bbox2):
        """计算两个边界框的相似度"""
        if not bbox1 or not bbox2 or len(bbox1) < 4 or len(bbox2) < 4:
            return 0.0

        try:
            x1_inter = max(bbox1[0], bbox2[0])
            y1_inter = max(bbox1[1], bbox2[1])
            x2_inter = min(bbox1[2], bbox2[2])
            y2_inter = min(bbox1[3], bbox2[3])

            if x2_inter <= x1_inter or y2_inter <= y1_inter:
                return 0.0

            inter_area = (x2_inter - x1_inter) * (y2_inter - y1_inter)
            area1 = (bbox1[2] - bbox1[0]) * (bbox1[3] - bbox1[1])
            area2 = (bbox2[2] - bbox2[0]) * (bbox2[3] - bbox2[1])
            union_area = area1 + area2 - inter_area

            return inter_area / union_area if union_area > 0 else 0.0
        except:
            return 0.0

    def text_similarity(self, text1, text2):
        """计算两个文本的相似度"""
        if not text1 or not text2:
            return 0.0

        set1 = set(text1.lower())
        set2 = set(text2.lower())
        intersection = len(set1.intersection(set2))
        union = len(set1.union(set2))

        return intersection / union if union > 0 else 0.0

    def predict(self, image_path, save_result=True, output_dir='results'):
        """
        综合预测：目标检测 + OCR文字识别
        真正的多任务架构：输入图像 -> 主干网络(Backbone) -> 特征金字塔(Neck)
        ├── 元器件检测头(YOLO head) -> 预测元器件类别和框
        └── 文字检测+识别头(OCR head) -> 预测文字框和字符内容
        """
        print(f"🎯 开始真正的多任务预测: {image_path}")

        # 读取图像
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError(f"无法读取图像: {image_path}")

        try:
            # 1. 使用神经网络多任务模型进行预测
            print("🧠 使用神经网络多任务模型...")

            # 预处理图像
            input_tensor = self.preprocess_image(image)

            # 设置为评估模式
            self.eval()

            with torch.no_grad():
                # 前向传播
                model_outputs = self.forward(input_tensor)

                # 解析元器件检测结果
                component_detections = self.parse_component_detections(
                    model_outputs['component_detections'],
                    input_tensor.shape[-2:],  # 输入尺寸
                    image.shape[:2]  # 原始图像尺寸
                )

                # 解析文字检测结果
                text_detections = self.parse_text_detections(
                    model_outputs['text_results'],
                    input_tensor.shape[-2:],  # 输入尺寸
                    image.shape[:2]  # 原始图像尺寸
                )

            print(f"✅ 神经网络预测完成: 检测到 {len(component_detections)} 个元器件, {len(text_detections)} 个文字区域")

        except Exception as e:
            print(f"⚠️ 神经网络预测失败，使用后备OCR引擎: {e}")

            # 2. 后备方案：使用外部OCR引擎
            component_detections = []
            text_detections = self.detect_text_with_all_ocr(image)

            # 转换格式
            text_detections = [
                {
                    'text': region['text'],
                    'confidence': region['confidence'],
                    'engine': region['engine'],
                    'detection_bbox': region['bbox'],
                    'detection_type': region['type']
                }
                for region in text_detections
                if 'text' in region and region['text'].strip()
            ]

        # 3. 构建最终结果
        final_result = {
            'image_path': image_path,
            'component_detections': component_detections,
            'text_recognition': text_detections,
            'detection_count': len(component_detections),
            'text_count': len(text_detections),
            'timestamp': time.time(),
            'model_type': 'neural_multitask'
        }

        print(f"✅ 多任务预测完成: 检测到 {final_result['detection_count']} 个元器件, {final_result['text_count']} 段文字")

        # 保存结果
        if save_result:
            self.save_prediction_result(final_result, image, output_dir)

        return final_result

    def draw_chinese_text(self, img, text, position, font_size=20, color=(255, 0, 0)):
        """使用PIL在图像上绘制中文文字"""
        try:
            img_pil = Image.fromarray(cv2.cvtColor(img, cv2.COLOR_BGR2RGB))
            draw = ImageDraw.Draw(img_pil)

            # 尝试加载中文字体
            try:
                font = ImageFont.truetype("C:/Windows/Fonts/simhei.ttf", font_size)
            except:
                try:
                    font = ImageFont.truetype("arial.ttf", font_size)
                except:
                    font = ImageFont.load_default()

            draw.text(position, text, font=font, fill=color)
            return cv2.cvtColor(np.array(img_pil), cv2.COLOR_RGB2BGR)
        except Exception as e:
            print(f"绘制中文文字失败: {e}")
            cv2.putText(img, text, position, cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)
            return img

    def parse_component_detections(self, component_outputs, input_size, original_size):
        """解析元器件检测结果"""
        # 这是一个占位符实现，实际应该解析神经网络输出
        # 目前返回空列表，因为我们主要使用外部OCR引擎
        return []

    def parse_text_detections(self, text_outputs, input_size, original_size):
        """解析文字检测结果"""
        # 这是一个占位符实现，实际应该解析神经网络输出
        # 目前返回空列表，因为我们主要使用外部OCR引擎
        return []

    def save_prediction_result(self, result, image, output_dir):
        """保存预测结果（可视化图像和JSON数据）"""
        os.makedirs(output_dir, exist_ok=True)
        image_name = Path(result['image_path']).stem
        result_image = image.copy()

        # 绘制元器件检测框 - 绿色框住元器件
        if 'component_detections' in result and result['component_detections']:
            for detection in result['component_detections']:
                if 'bbox' in detection:
                    bbox = detection['bbox']
                    confidence = detection.get('confidence', 0.0)
                    class_id = detection.get('class_id', 0)

                    # 绘制元器件检测框 - 绿色
                    cv2.rectangle(result_image, (bbox[0], bbox[1]), (bbox[2], bbox[3]), (0, 255, 0), 2)
                    cv2.putText(result_image, f'Class:{class_id} {confidence:.2f}',
                               (bbox[0], bbox[1]-10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)

        # 绘制OCR结果 - 蓝色框住文字
        for text_result in result['text_recognition']:
            bbox = text_result.get('detection_bbox', text_result.get('bbox', []))
            text = text_result['text']
            confidence = text_result['confidence']

            if len(bbox) >= 4:
                # 绘制文字区域 - 蓝色
                cv2.rectangle(result_image, (bbox[0], bbox[1]), (bbox[2], bbox[3]), (255, 0, 0), 2)

                # 添加识别的文字
                text_to_show = f'{text} ({confidence:.2f})'
                result_image = self.draw_chinese_text(result_image, text_to_show,
                                                    (bbox[0], bbox[3]+20), font_size=16, color=(255, 0, 0))

        # 保存结果
        cv2.imwrite(os.path.join(output_dir, f'{image_name}_result.jpg'), result_image)

        # 保存JSON结果
        json_result = {
            'image_path': result['image_path'],
            'detection_count': result['detection_count'],
            'text_count': result['text_count'],
            'text_recognition': result['text_recognition'],
            'timestamp': result['timestamp']
        }

        with open(os.path.join(output_dir, f'{image_name}_result.json'), 'w', encoding='utf-8') as f:
            json.dump(json_result, f, ensure_ascii=False, indent=2)

    def _json_serializer(self, obj):
        """
        JSON序列化辅助函数，处理numpy类型
        """
        import numpy as np
        if isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif hasattr(obj, 'item'):  # torch tensor
            return obj.item()
        elif hasattr(obj, 'tolist'):  # torch tensor
            return obj.tolist()
        raise TypeError(f"Object of type {type(obj)} is not JSON serializable")


def train_yolo_model():
    """
    训练YOLO目标检测模型
    """
    print("🚀 开始训练YOLO目标检测模型...")

    # 检查GPU可用性
    device = '0' if torch.cuda.is_available() else 'cpu'
    print(f"📱 使用设备: {'GPU' if device == '0' else 'CPU'}")

    # 加载预训练模型
    model = YOLO(r'yolo11s.pt')

    # 高精度训练参数配置
    training_args = {
        'data': r'yqjdataset/data.yaml',
        'epochs': 10,  # 增加训练轮数以提高精度
        'imgsz': 640,
        'batch': 16,
        'device': device,
        'optimizer': 'AdamW',
        'lr0': 0.001,
        'lrf': 0.01,  # 最终学习率
        'momentum': 0.937,
        'weight_decay': 0.0005,
        'warmup_epochs': 3.0,
        'warmup_momentum': 0.8,
        'warmup_bias_lr': 0.1,
        'close_mosaic': 15,
        'workers': 4,
        'amp': True,  # 自动混合精度
        'single_cls': False,
        'project': 'high_precision_detection',
        'name': 'yolo11s_ocr_integrated',
        'save': True,
        'save_period': 10,  # 每10个epoch保存一次
        'val': True,
        'plots': True,
        'verbose': True,
        # 数据增强参数 - 提高模型泛化能力
        'hsv_h': 0.015,
        'hsv_s': 0.7,
        'hsv_v': 0.4,
        'degrees': 0.0,
        'translate': 0.1,
        'scale': 0.5,
        'shear': 0.0,
        'perspective': 0.0,
        'flipud': 0.0,
        'fliplr': 0.5,
        'mosaic': 1.0,
        'mixup': 0.0,
        'copy_paste': 0.0,
        # 损失函数权重
        'box': 7.5,
        'cls': 0.5,
        'dfl': 1.5,
    }

    # 开始训练
    results = model.train(**training_args)

    print("✅ YOLO模型训练完成!")
    return model, results

def create_multitask_model(yolo_model_path=None):
    """
    创建多任务模型 (YOLO目标检测 + CnOCR文字识别)
    """
    print("🚀 创建多任务模型...")

    # 创建多任务模型 - 修正参数传递
    model = MultiTaskModel(num_classes=47, num_chars=6000, pretrained=True)

    print("✅ 多任务模型创建完成!")
    print("📝 使用神经网络多任务架构进行目标检测 + OCR文字识别")

    return model




def save_integrated_model(model, save_path: str):
    """保存整合的YOLO+OCR模型"""
    print(f"💾 保存整合模型到: {save_path}")

    try:
        # 确保保存目录存在
        os.makedirs(os.path.dirname(save_path), exist_ok=True)

        # 获取YOLO模型路径
        yolo_model_path = None
        if hasattr(model, 'yolo_model') and hasattr(model.yolo_model, 'model_path'):
            yolo_model_path = model.yolo_model.model_path
        elif hasattr(model, 'yolo_model'):
            # 尝试从YOLO模型获取路径
            yolo_model_path = getattr(model.yolo_model, 'ckpt_path', 'yolo11s.pt')
        else:
            yolo_model_path = 'yolo11s.pt'

        # 创建模型数据字典
        model_data = {
            'model_type': 'MultiTaskModel',
            'yolo_model_path': yolo_model_path,
            'detection_conf_threshold': getattr(model, 'detection_conf_threshold', 0.15),
            'ocr_confidence_threshold': getattr(model, 'ocr_confidence_threshold', 0.05),
            'num_classes': getattr(model, 'num_classes', 47),
            'timestamp': time.time(),
            'version': '1.0'
        }

        # 尝试保存模型状态字典（如果可能）
        try:
            model_data['model_state_dict'] = model.state_dict()
        except Exception as e:
            print(f"⚠️  无法保存模型状态字典: {e}")

        # 保存模型
        torch.save(model_data, save_path)
        print(f"✅ 整合模型保存成功")
        print(f"   📁 保存路径: {save_path}")
        print(f"   🎯 YOLO模型: {yolo_model_path}")

    except Exception as e:
        print(f"❌ 保存整合模型失败: {e}")
        raise

def load_integrated_model(model_path: str):
    """加载整合的YOLO+OCR模型"""
    print(f"📂 加载整合模型配置: {model_path}")

    try:
        # 检查是否是JSON配置文件
        if model_path.endswith('.json'):
            import json
            with open(model_path, 'r', encoding='utf-8') as f:
                config = json.load(f)

            if config.get('model_type') == 'MultiTaskWrapper':
                print("🔧 加载多任务包装模型...")
                yolo_model_path = config['yolo_model_path']

                # 创建多任务模型
                model = MultiTaskModel(yolo_model_path)

                # 恢复配置
                model.detection_conf_threshold = config.get('detection_conf_threshold', 0.15)
                model.ocr_confidence_threshold = config.get('ocr_confidence_threshold', 0.05)

                print(f"✅ 多任务包装模型加载完成")
                print(f"   🎯 YOLO模型: {yolo_model_path}")

                return model
            else:
                print(f"❌ 不支持的模型类型: {config.get('model_type')}")
                return None
        else:
            # 尝试加载PyTorch模型文件
            print("🔧 尝试加载PyTorch模型文件...")
            model_data = torch.load(model_path, map_location='cpu')

            if isinstance(model_data, dict) and 'model_type' in model_data:
                model_type = model_data.get('model_type')
                print(f"🔍 检测到模型类型: {model_type}")

                if model_type in ['MultiTaskModel', 'MultiTaskWrapper']:
                    yolo_model_path = model_data.get('yolo_model_path', 'yolo11s.pt')

                    # 创建多任务模型
                    model = MultiTaskModel(yolo_model_path)

                    # 恢复配置
                    model.detection_conf_threshold = model_data.get('detection_conf_threshold', 0.15)
                    model.ocr_confidence_threshold = model_data.get('ocr_confidence_threshold', 0.05)

                    # 尝试加载状态字典
                    if 'model_state_dict' in model_data:
                        try:
                            model.load_state_dict(model_data['model_state_dict'])
                            print("✅ 成功加载模型权重")
                        except Exception as e:
                            print(f"⚠️  加载权重失败，使用默认权重: {e}")

                    print(f"✅ 多任务模型加载完成")
                    print(f"   🎯 YOLO模型: {yolo_model_path}")

                    return model
                else:
                    print(f"❌ 不支持的模型类型: {model_type}")
                    return None
            else:
                print(f"❌ 无效的模型文件格式")
                return None

    except Exception as e:
        print(f"❌ 加载模型失败: {e}")
        return None

def create_legacy_multitask_model(yolo_model_path=None):
    """
    创建传统多任务模型 (YOLO目标检测 + 外部OCR文字识别)
    """
    if yolo_model_path is None:
        # 使用训练好的最佳模型
        yolo_model_path = 'high_precision_detection/yolo11s_ocr_integrated/weights/best.pt'

        # 如果不存在，使用预训练模型
        if not os.path.exists(yolo_model_path):
            yolo_model_path = 'yolo11s.pt'

    print(f"🔧 创建传统多任务模型，使用YOLO权重: {yolo_model_path}")

    # 创建多任务模型
    legacy_model = MultiTaskModel(yolo_model_path)

    print("✅ 传统多任务模型创建完成!")
    return legacy_model


def test_integrated_model(model, test_images_dir='yqjdataset/test/images', output_dir='integrated_results'):
    """
    测试整合模型的性能
    """
    print(f"🧪 开始测试整合模型...")

    # 获取测试图像
    test_images = []
    for ext in ['*.jpg', '*.jpeg', '*.png']:
        test_images.extend(Path(test_images_dir).glob(ext))

    if not test_images:
        print(f"❌ 在 {test_images_dir} 中未找到测试图像")
        return

    print(f"📊 找到 {len(test_images)} 张测试图像")

    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)

    # 测试模型
    results = []
    for i, image_path in enumerate(test_images[:10]):  # 测试前10张图像
        try:
            result = model.predict(str(image_path), save_result=True, output_dir=output_dir)
            results.append(result)

            # 打印结果摘要
            detection_count = 0
            if len(result['detections']) > 0:
                detection_result = result['detections'][0]
                if hasattr(detection_result, 'boxes') and detection_result.boxes is not None:
                    detection_count = len(detection_result.boxes)
            text_count = len(result['text_recognition'])

            print(f"图像 {i+1}: 检测到 {detection_count} 个目标, 识别到 {text_count} 段文字")

        except Exception as e:
            print(f"❌ 处理图像 {image_path} 时出错: {e}")

    print(f"✅ 测试完成! 结果保存在 {output_dir}")
    return results


def main():
    """
    主函数：训练和测试整合模型
    """
    print("🎯 高精度目标检测+OCR文字识别整合训练系统")
    print("=" * 60)
    print("📋 架构说明:")
    print("   输入图像 -> 主干网络(Backbone) -> 特征金字塔(Neck)")
    print("   ├── 元器件检测头(YOLO head) -> 预测元器件类别和框")
    print("   └── 文字检测+识别头(OCR head) -> 预测文字框和字符内容")
    print("=" * 60)

    # 步骤1: 训练YOLO模型
    print("\n📍 步骤1: 训练高精度YOLO目标检测模型")
    try:
        _, _ = train_yolo_model()
        print("✅ YOLO模型训练完成")
    except Exception as e:
        print(f"⚠️ YOLO训练出错，使用预训练模型: {e}")

    # 步骤2: 创建多任务模型
    print("\n📍 步骤2: 创建多任务模型 (目标检测+OCR)")
    try:
        integrated_model = create_multitask_model()
        print("✅ 多任务模型创建完成")
    except Exception as e:
        print(f"⚠️ 多任务模型创建出错: {e}")
        integrated_model = None

    # 步骤3: 保存整合模型
    if integrated_model is not None:
        print("\n📍 步骤3: 保存整合模型")
        try:
            save_path = 'models/integrated_yolo_ocr_model.pt'
            save_integrated_model(integrated_model, save_path)
            print(f"✅ 整合模型已保存到: {save_path}")
        except Exception as e:
            print(f"⚠️ 保存整合模型失败: {e}")

    print("\n🎉 训练完成!")
    print("📁 检查以下目录获取结果:")
    print("   - high_precision_detection/yolo11s_ocr_integrated/ (YOLO训练结果)")
    print("   - models/integrated_yolo_ocr_model.pt (整合模型)")
    print("\n🔧 模型特点:")
    print("   ✓ 输入图像 -> 主干网络(Backbone) -> 特征金字塔(Neck)")
    print("   ✓ 元器件检测头(YOLO head) -> 预测元器件类别和框")
    print("   ✓ 文字检测+识别头(OCR head) -> 预测文字框和字符内容")
    print("   ✓ 多OCR引擎融合，提高文字识别精度")

    return integrated_model


def demo_multitask_prediction(image_path='DaYuanTuZ_0.png'):
    """
    演示多任务模型的预测功能
    """
    print(f"🎬 演示多任务模型预测功能")
    print(f"📸 测试图像: {image_path}")

    if not os.path.exists(image_path):
        print(f"❌ 图像文件不存在: {image_path}")
        return

    # 创建模型
    model = create_multitask_model()

    # 进行预测
    try:
        result = model.predict(image_path, save_result=True, output_dir='demo_results')

        # 打印结果摘要
        detection_count = 0
        if len(result['detections']) > 0:
            detection_result = result['detections'][0]
            if hasattr(detection_result, 'boxes') and detection_result.boxes is not None:
                detection_count = len(detection_result.boxes)
        text_count = len(result['text_recognition'])
        cnocr_text_regions = result.get('cnocr_text_regions', 0)
        detection_text_regions = result.get('detection_text_regions', 0)

        print(f"\n📊 预测结果摘要:")
        print(f"   🎯 检测到元器件: {detection_count} 个")
        print(f"   📝 识别到文字: {text_count} 段")
        print(f"   🧠 CnOCR文字区域: {cnocr_text_regions} 个")
        print(f"   🔍 检测框文字区域: {detection_text_regions} 个")

        if text_count > 0:
            print(f"\n📝 识别到的文字内容:")
            for i, text_result in enumerate(result['text_recognition'][:5]):  # 显示前5个
                print(f"   {i+1}. {text_result['text']} (置信度: {text_result['confidence']:.2f}, 引擎: {text_result['engine']})")

        print(f"\n💾 结果已保存到: demo_results/")

    except Exception as e:
        import traceback
        print(f"❌ 预测过程出错: {e}")
        print(f"错误详情: {traceback.format_exc()}")


if __name__ == '__main__':
    import argparse

    parser = argparse.ArgumentParser(description='YOLO+OCR整合模型训练和预测')
    parser.add_argument('--mode', type=str, default='train',
                       choices=['train', 'predict', 'create_integrated', 'demo'],
                       help='运行模式: train(训练), predict(预测), create_integrated(创建整合模型), demo(演示)')
    parser.add_argument('--image', type=str, default='DaYuanTuZ_0.png',
                       help='预测时使用的图像路径')
    parser.add_argument('--yolo_model', type=str, default=None,
                       help='YOLO模型路径')
    parser.add_argument('--save_path', type=str, default='models/integrated_yolo_ocr_model.pt',
                       help='整合模型保存路径')
    parser.add_argument('--load_model', type=str, default=None,
                       help='加载已保存的整合模型路径')

    args = parser.parse_args()

    if args.mode == 'train':
        # 完整训练模式
        print("🚀 开始完整训练流程...")
        model = main()

    elif args.mode == 'create_multitask':
        # 创建多任务模型模式
        print("🔧 创建多任务YOLO+OCR模型...")
        model = create_multitask_model(yolo_model_path=args.yolo_model)
        print(f"✅ 多任务模型已创建")
        print("📝 现在您可以使用以下命令进行预测:")
        print(f"   python train.py --mode predict --image your_image.png")

    elif args.mode == 'predict':
        # 预测模式
        print(f"🎯 预测模式，图像: {args.image}")

        if args.load_model:
            # 使用保存的整合模型
            print(f"📂 加载整合模型: {args.load_model}")
            model = load_integrated_model(args.load_model)
        else:
            # 使用传统多任务模型
            print("🔧 创建传统多任务模型...")
            model = create_legacy_multitask_model(args.yolo_model)

        # 进行预测
        if not os.path.exists(args.image):
            print(f"❌ 图像文件不存在: {args.image}")
        else:
            try:
                result = model.predict(args.image, save_result=True, output_dir='demo_results')

                # 打印结果摘要
                detection_count = 0
                if len(result['detections']) > 0:
                    detection_result = result['detections'][0]
                    if hasattr(detection_result, 'boxes') and detection_result.boxes is not None:
                        detection_count = len(detection_result.boxes)
                text_count = len(result['text_recognition'])

                print(f"\n📊 预测结果摘要:")
                print(f"   🎯 检测到元器件: {detection_count} 个")
                print(f"   📝 识别到文字: {text_count} 段")

                if text_count > 0:
                    print(f"\n📝 识别到的文字内容:")
                    for i, text_result in enumerate(result['text_recognition'][:5]):
                        print(f"   {i+1}. {text_result['text']} (置信度: {text_result['confidence']:.2f}, 引擎: {text_result['engine']})")

                print(f"\n💾 结果已保存到: demo_results/")

            except Exception as e:
                import traceback
                print(f"❌ 预测过程出错: {e}")
                print(f"错误详情: {traceback.format_exc()}")

    elif args.mode == 'demo':
        # 演示模式
        demo_multitask_prediction(args.image)

    else:
        print(f"❌ 未知模式: {args.mode}")
        parser.print_help()