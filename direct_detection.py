#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接检测脚本 - 支持加载自定义MultiTaskModel多任务模型

使用示例:
1. 使用默认参数:
   python direct_detection.py

2. 指定自定义模型和图像:
   python direct_detection.py --model models/integrated_yolo_ocr_model.pt --image your_image.jpg

3. 调整检测参数:
   python direct_detection.py --conf 0.3 --imgsz 800 --overlap 0.3

4. 使用训练好的YOLO模型:
   python direct_detection.py --model high_precision_detection/yolo11s_ocr_integrated8/weights/best.pt

支持的模型类型:
- MultiTaskModel (推荐): 集成YOLO检测和多引擎OCR识别
- 标准YOLO模型: 仅支持目标检测
- JSON配置文件: 多任务模型配置
"""

import os
import torch
import cv2
import time
from PIL import Image
import json
import numpy as np
from typing import List, Tuple, Dict

# 导入自定义模型类
try:
    from train import MultiTaskModel, load_integrated_model
    CUSTOM_MODEL_AVAILABLE = True
except ImportError:
    print("⚠️  无法导入自定义模型类，将只支持标准YOLO模型")
    CUSTOM_MODEL_AVAILABLE = False

def check_gpu_memory():
    """检查GPU内存"""
    if torch.cuda.is_available():
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3  # GB
        print(f"🖥️  GPU总内存: {gpu_memory:.1f} GB")
        free_memory = torch.cuda.memory_reserved(0) / 1024**3
        print(f"💾 GPU可用内存: {free_memory:.1f} GB")
        return gpu_memory
    else:
        print("❌ 未检测到CUDA GPU")
        return 0

def preprocess_image(image_path, target_size=640):
    """预处理图像 - 根据训练尺寸进行适当缩放"""
    try:
        with Image.open(image_path) as img:
            width, height = img.size
            max_dim = max(width, height)

            print(f"📐 原始图像尺寸: {width} x {height}")

            # 如果图像太大，缩放到合适的训练尺寸
            if max_dim > target_size:
                scale = target_size / max_dim
                new_width = int(width * scale)
                new_height = int(height * scale)

                print(f"🔄 缩放图像到训练兼容尺寸: {new_width} x {new_height} (缩放比例: {scale:.3f})")

                resized_img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
                temp_path = "temp_resized_detection.jpg"
                resized_img.save(temp_path, quality=95)

                return temp_path, scale
            else:
                print(f"📐 图像尺寸适合，无需缩放")
                return image_path, 1.0

    except Exception as e:
        print(f"❌ 图像预处理错误: {e}")
        return image_path, 1.0

def create_sliding_windows(image_width: int, image_height: int, window_size: int = 640, overlap_ratio: float = 0.2) -> List[Tuple[int, int, int, int]]:
    """
    创建滑动窗口坐标列表

    Args:
        image_width: 图像宽度
        image_height: 图像高度
        window_size: 窗口大小
        overlap_ratio: 重叠比例 (0.0-1.0)

    Returns:
        List of (x1, y1, x2, y2) 窗口坐标
    """
    windows = []
    step_size = int(window_size * (1 - overlap_ratio))

    # 如果图像小于窗口大小，直接返回整个图像
    if image_width <= window_size and image_height <= window_size:
        return [(0, 0, image_width, image_height)]

    # 计算窗口数量
    x_windows = max(1, (image_width - window_size) // step_size + 1)
    y_windows = max(1, (image_height - window_size) // step_size + 1)

    print(f"🔲 创建滑动窗口: {x_windows} x {y_windows} = {x_windows * y_windows} 个窗口")
    print(f"   - 窗口大小: {window_size}x{window_size}")
    print(f"   - 重叠比例: {overlap_ratio:.1%}")
    print(f"   - 步长: {step_size}")

    for y in range(y_windows):
        for x in range(x_windows):
            x1 = x * step_size
            y1 = y * step_size
            x2 = min(x1 + window_size, image_width)
            y2 = min(y1 + window_size, image_height)

            # 确保窗口有足够的大小
            if x2 - x1 >= window_size // 2 and y2 - y1 >= window_size // 2:
                windows.append((x1, y1, x2, y2))

    return windows

def non_max_suppression_custom(detections: List[Dict], iou_threshold: float = 0.5) -> List[Dict]:
    """
    自定义非极大值抑制，去除重复检测

    Args:
        detections: 检测结果列表
        iou_threshold: IoU阈值

    Returns:
        去重后的检测结果
    """
    if not detections:
        return []

    # 按置信度排序
    detections = sorted(detections, key=lambda x: x['confidence'], reverse=True)

    keep = []
    while detections:
        current = detections.pop(0)
        keep.append(current)

        # 计算与剩余检测框的IoU
        remaining = []
        for det in detections:
            if calculate_iou(current['bbox'], det['bbox']) < iou_threshold:
                remaining.append(det)

        detections = remaining

    return keep

def calculate_iou(box1: List[float], box2: List[float]) -> float:
    """
    计算两个边界框的IoU

    Args:
        box1: [x1, y1, x2, y2]
        box2: [x1, y1, x2, y2]

    Returns:
        IoU值
    """
    x1 = max(box1[0], box2[0])
    y1 = max(box1[1], box2[1])
    x2 = min(box1[2], box2[2])
    y2 = min(box1[3], box2[3])

    if x2 <= x1 or y2 <= y1:
        return 0.0

    intersection = (x2 - x1) * (y2 - y1)
    area1 = (box1[2] - box1[0]) * (box1[3] - box1[1])
    area2 = (box2[2] - box2[0]) * (box2[3] - box2[1])
    union = area1 + area2 - intersection

    return intersection / union if union > 0 else 0.0

class YOLOModelWrapper:
    """
    YOLO模型包装器，用于兼容MultiTaskModel接口
    """
    def __init__(self, yolo_model_path):
        from ultralytics import YOLO
        self.yolo_model = YOLO(yolo_model_path)
        self.detection_conf_threshold = 0.15
        self.ocr_confidence_threshold = 0.05
        self.ocr_engines = {}

    def predict(self, image_path, save_result=False, output_dir='results'):
        """兼容MultiTaskModel的predict接口"""
        results = self.yolo_model.predict(
            image_path,
            conf=self.detection_conf_threshold,
            save=False,
            verbose=False
        )

        component_detections = []
        if len(results) > 0:
            result = results[0]
            if hasattr(result, 'boxes') and result.boxes is not None:
                for box in result.boxes:
                    x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                    confidence = float(box.conf[0])
                    class_id = int(box.cls[0])

                    detection = {
                        'class_id': class_id,
                        'confidence': confidence,
                        'bbox': [float(x1), float(y1), float(x2), float(y2)]
                    }
                    component_detections.append(detection)

        return {
            'component_detections': component_detections,
            'text_recognition': [],  # YOLO模型不支持OCR
            'detection_count': len(component_detections),
            'text_count': 0
        }

def load_custom_model(model_path):
    """
    加载自定义的MultiTaskModel模型

    Args:
        model_path: 模型文件路径

    Returns:
        model: 加载的模型实例，如果失败返回None
    """
    if not CUSTOM_MODEL_AVAILABLE:
        print("❌ 自定义模型类不可用")
        return None

    try:
        print(f"🔧 尝试加载自定义多任务模型: {model_path}")

        # 检查模型文件
        if not os.path.exists(model_path):
            print(f"❌ 模型文件不存在: {model_path}")
            return None

        # 检查模型文件大小
        file_size = os.path.getsize(model_path) / (1024 * 1024)  # MB
        print(f"📁 模型文件大小: {file_size:.1f} MB")

        # 首先尝试使用JSON配置文件加载
        if model_path.endswith('.json'):
            try:
                model = load_integrated_model(model_path)
                if model is not None:
                    print(f"✅ 成功从JSON配置加载MultiTaskModel")
                    return model
            except Exception as e:
                print(f"⚠️  JSON配置加载失败: {e}")

        # 尝试直接创建MultiTaskModel
        try:
            print(f"🔄 尝试创建新的MultiTaskModel...")

            # 检查是否有训练好的YOLO模型
            yolo_paths = [
                'high_precision_detection/yolo11s_ocr_integrated8/weights/best.pt',
                'high_precision_detection/yolo11s_ocr_integrated/weights/best.pt',
                'yolo11s.pt',
                'yolov8n.pt'
            ]

            yolo_model_path = None
            for path in yolo_paths:
                if os.path.exists(path):
                    yolo_model_path = path
                    break

            if yolo_model_path is None:
                yolo_model_path = 'yolo11s.pt'  # 使用默认模型

            print(f"🎯 使用YOLO模型: {yolo_model_path}")
            # 修正MultiTaskModel构造函数调用
            model = MultiTaskModel(num_classes=47, num_chars=6000, pretrained=True)

            # 如果有自定义YOLO模型，可以在这里设置
            if hasattr(model, 'yolo_model_path'):
                model.yolo_model_path = yolo_model_path

            print(f"✅ 成功创建MultiTaskModel")
            return model

        except Exception as e:
            print(f"❌ 创建MultiTaskModel失败: {e}")

        # 尝试加载PyTorch模型数据
        try:
            model_data = torch.load(model_path, map_location='cpu')

            if isinstance(model_data, dict) and 'model_type' in model_data:
                model_type = model_data.get('model_type', 'Unknown')
                print(f"🔍 检测到模型类型: {model_type}")

                if model_type in ['MultiTaskModel', 'MultiTaskWrapper']:
                    print(f"🔄 尝试从保存的数据加载MultiTaskModel...")
                    try:
                        yolo_path = model_data.get('yolo_model_path', 'yolo11s.pt')
                        if not os.path.exists(yolo_path):
                            yolo_path = 'yolo11s.pt'

                        # 修正MultiTaskModel构造函数调用
                        model = MultiTaskModel(num_classes=47, num_chars=6000, pretrained=True)

                        # 设置YOLO模型路径
                        if hasattr(model, 'yolo_model_path'):
                            model.yolo_model_path = yolo_path

                        # 尝试加载状态字典
                        if 'model_state_dict' in model_data:
                            try:
                                model.load_state_dict(model_data['model_state_dict'])
                                print(f"✅ 成功加载模型权重")
                            except Exception as e:
                                print(f"⚠️  加载权重失败，使用默认权重: {e}")

                        # 恢复配置
                        if hasattr(model, 'detection_conf_threshold'):
                            model.detection_conf_threshold = model_data.get('detection_conf_threshold', 0.15)
                        if hasattr(model, 'ocr_confidence_threshold'):
                            model.ocr_confidence_threshold = model_data.get('ocr_confidence_threshold', 0.05)

                        print(f"✅ 成功加载MultiTaskModel")
                        return model

                    except Exception as e:
                        print(f"❌ 加载MultiTaskModel失败: {e}")

                else:
                    print(f"⚠️  不支持的模型类型: {model_type}，尝试创建新模型")
                    # 创建新的MultiTaskModel
                    yolo_path = model_data.get('yolo_model_path', 'yolo11s.pt')
                    if not os.path.exists(yolo_path):
                        yolo_path = 'yolo11s.pt'
                    # 修正MultiTaskModel构造函数调用
                    model = MultiTaskModel(num_classes=47, num_chars=6000, pretrained=True)
                    if hasattr(model, 'yolo_model_path'):
                        model.yolo_model_path = yolo_path
                    print(f"✅ 创建新的MultiTaskModel成功")
                    return model

            else:
                print(f"⚠️  无效的模型文件格式，创建新模型")
                # 修正MultiTaskModel构造函数调用
                model = MultiTaskModel(num_classes=47, num_chars=6000, pretrained=True)
                print(f"✅ 创建新的MultiTaskModel成功")
                return model

        except Exception as e:
            print(f"⚠️  加载模型数据失败: {e}")
            # 最后尝试创建默认模型
            try:
                # 修正MultiTaskModel构造函数调用
                model = MultiTaskModel(num_classes=47, num_chars=6000, pretrained=True)
                print(f"✅ 创建默认MultiTaskModel成功")
                return model
            except Exception as e2:
                print(f"❌ 创建默认模型也失败: {e2}")
                # 尝试创建YOLO包装器作为最后的备选
                try:
                    print(f"🔄 尝试创建YOLO包装器...")
                    if model_path.endswith('.pt') and os.path.exists(model_path):
                        wrapper = YOLOModelWrapper(model_path)
                        print(f"✅ 创建YOLO包装器成功")
                        return wrapper
                    else:
                        wrapper = YOLOModelWrapper('yolo11s.pt')
                        print(f"✅ 创建默认YOLO包装器成功")
                        return wrapper
                except Exception as e3:
                    print(f"❌ 创建YOLO包装器也失败: {e3}")

    except Exception as e:
        print(f"❌ 加载自定义模型失败: {e}")
        # 尝试创建YOLO包装器作为备选
        try:
            print(f"🔄 尝试创建YOLO包装器作为备选...")
            if model_path.endswith('.pt') and os.path.exists(model_path):
                wrapper = YOLOModelWrapper(model_path)
                print(f"✅ 创建YOLO包装器成功")
                return wrapper
            else:
                wrapper = YOLOModelWrapper('yolo11s.pt')
                print(f"✅ 创建默认YOLO包装器成功")
                return wrapper
        except Exception as e2:
            print(f"❌ 创建YOLO包装器也失败: {e2}")

    return None

def detect_with_unified_sliding_window(model, image_path, save_result=True, output_dir='results',
                                     window_size=640, overlap_ratio=0.3, use_sliding_window=True):
    """
    使用统一的滑动窗口进行YOLO和OCR检测

    Args:
        model: 自定义模型实例
        image_path: 图像路径
        save_result: 是否保存结果
        output_dir: 输出目录
        window_size: 滑动窗口大小
        overlap_ratio: 重叠比例
        use_sliding_window: 是否使用滑动窗口

    Returns:
        detections: 检测结果列表
        text_results: 文字识别结果列表
    """
    try:
        print(f"🎯 使用统一滑动窗口进行检测: {image_path}")

        # 读取图像信息
        image = cv2.imread(image_path)
        if image is None:
            print(f"❌ 无法读取图像: {image_path}")
            return [], []

        height, width = image.shape[:2]
        print(f"📐 图像尺寸: {width} x {height}")

        all_detections = []
        all_text_results = []

        # 判断是否使用滑动窗口
        if use_sliding_window and (width > window_size or height > window_size):
            print(f"🔍 图像较大，使用统一滑动窗口检测...")
            print(f"   窗口大小: {window_size}x{window_size}")
            print(f"   重叠比例: {overlap_ratio}")

            # 计算滑动窗口参数
            step_size = int(window_size * (1 - overlap_ratio))
            window_count = 0

            # 滑动窗口遍历
            for y in range(0, height, step_size):
                for x in range(0, width, step_size):
                    # 计算窗口边界
                    x_end = min(x + window_size, width)
                    y_end = min(y + window_size, height)

                    # 如果窗口太小，跳过
                    if (x_end - x) < window_size // 2 or (y_end - y) < window_size // 2:
                        continue

                    window_count += 1
                    print(f"   处理窗口 {window_count}: [{x}:{x_end}, {y}:{y_end}]")

                    # 提取窗口
                    window = image[y:y_end, x:x_end]

                    # 保存临时窗口图像
                    temp_window_path = f"temp_window_{window_count}.jpg"
                    cv2.imwrite(temp_window_path, window)

                    try:
                        # 1. 对窗口进行检测
                        window_detections = []

                        # 检查模型类型并调用相应的检测方法
                        if hasattr(model, 'predict'):
                            # MultiTaskModel 类型，使用predict方法
                            try:
                                window_result = model.predict(temp_window_path, save_result=False)
                                if 'component_detections' in window_result:
                                    for detection in window_result['component_detections']:
                                        if 'bbox' in detection:
                                            bbox = detection['bbox']
                                            # 转换到原图坐标
                                            adjusted_detection = {
                                                'class_id': detection.get('class_id', 0),
                                                'confidence': detection.get('confidence', 0.0),
                                                'bbox': [float(bbox[0] + x), float(bbox[1] + y),
                                                        float(bbox[2] + x), float(bbox[3] + y)]
                                            }
                                            all_detections.append(adjusted_detection)
                            except Exception as e:
                                print(f"   MultiTaskModel预测失败: {e}")

                        elif hasattr(model, 'yolo_model'):
                            # 如果模型有yolo_model属性，使用YOLO检测
                            yolo_results = model.yolo_model.predict(
                                temp_window_path,
                                conf=getattr(model, 'detection_conf_threshold', 0.15),
                                save=False,
                                verbose=False
                            )

                            # 处理YOLO检测结果
                            if len(yolo_results) > 0:
                                result = yolo_results[0]
                                if hasattr(result, 'boxes') and result.boxes is not None:
                                    for box in result.boxes:
                                        # 获取窗口内的坐标
                                        x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                                        confidence = float(box.conf[0])
                                        class_id = int(box.cls[0])

                                        # 转换到原图坐标
                                        detection = {
                                            'class_id': class_id,
                                            'confidence': confidence,
                                            'bbox': [float(x1 + x), float(y1 + y), float(x2 + x), float(y2 + y)]
                                        }
                                        all_detections.append(detection)

                        # 2. 对窗口进行OCR检测
                        window_text_results = []

                        # 检查模型类型并调用相应的OCR方法
                        if hasattr(model, 'detect_text_with_all_ocr'):
                            # MultiTaskModel 类型
                            window_text_results = model.detect_text_with_all_ocr(window)
                        elif hasattr(model, 'ocr_engines') and model.ocr_engines:
                            # TrueIntegratedYOLOOCRModel 类型，直接使用OCR引擎
                            try:
                                # 使用CnOCR
                                if 'cnocr' in model.ocr_engines:
                                    from PIL import Image
                                    # 转换为PIL图像
                                    if len(window.shape) == 3:
                                        window_pil = Image.fromarray(cv2.cvtColor(window, cv2.COLOR_BGR2RGB))
                                    else:
                                        window_pil = Image.fromarray(window)

                                    cnocr_results = model.ocr_engines['cnocr'].ocr(window_pil)
                                    for result in cnocr_results:
                                        text = result.get('text', '')
                                        confidence = result.get('score', 0.0)
                                        if confidence > model.ocr_confidence_threshold and text.strip():
                                            # 获取位置信息
                                            position = result.get('position', [])
                                            if len(position) >= 4:
                                                # 转换为bbox格式 [x1, y1, x2, y2]
                                                x_coords = [point[0] for point in position]
                                                y_coords = [point[1] for point in position]
                                                bbox = [
                                                    int(min(x_coords)), int(min(y_coords)),
                                                    int(max(x_coords)), int(max(y_coords))
                                                ]
                                                window_text_results.append({
                                                    'text': text,
                                                    'confidence': confidence,
                                                    'detection_bbox': bbox,
                                                    'engine': 'cnocr'
                                                })

                                # 使用EasyOCR
                                if 'easyocr' in model.ocr_engines:
                                    easyocr_results = model.ocr_engines['easyocr'].readtext(window)
                                    for bbox, text, confidence in easyocr_results:
                                        if text.strip() and confidence > model.ocr_confidence_threshold:
                                            # 转换bbox格式
                                            x_coords = [point[0] for point in bbox]
                                            y_coords = [point[1] for point in bbox]
                                            x1, y1 = int(min(x_coords)), int(min(y_coords))
                                            x2, y2 = int(max(x_coords)), int(max(y_coords))

                                            window_text_results.append({
                                                'text': text,
                                                'confidence': confidence,
                                                'detection_bbox': [x1, y1, x2, y2],
                                                'engine': 'easyocr'
                                            })

                            except Exception as e:
                                print(f"   窗口OCR检测失败: {e}")

                        # 调整OCR结果坐标到原图坐标系
                        for text_result in window_text_results:
                            if 'bbox' in text_result or 'detection_bbox' in text_result:
                                bbox_key = 'bbox' if 'bbox' in text_result else 'detection_bbox'
                                bbox = text_result[bbox_key]
                                if len(bbox) >= 4:
                                    # 转换到原图坐标
                                    adjusted_bbox = [
                                        bbox[0] + x, bbox[1] + y,
                                        bbox[2] + x, bbox[3] + y
                                    ]
                                    text_result[bbox_key] = adjusted_bbox
                                    all_text_results.append(text_result)

                    except Exception as e:
                        print(f"   窗口 {window_count} 检测失败: {e}")

                    finally:
                        # 清理临时文件
                        if os.path.exists(temp_window_path):
                            os.remove(temp_window_path)

            # 对检测结果进行NMS去重
            print(f"🔄 统一滑动窗口检测完成，进行去重...")
            print(f"   - YOLO原始检测数: {len(all_detections)}")
            print(f"   - OCR原始检测数: {len(all_text_results)}")

            # YOLO检测结果去重
            all_detections = non_max_suppression_custom(all_detections, iou_threshold=0.5)

            # OCR结果去重（如果模型有相应方法）
            if hasattr(model, 'filter_and_deduplicate_text_results'):
                all_text_results = model.filter_and_deduplicate_text_results(all_text_results)

            print(f"   - YOLO去重后检测数: {len(all_detections)}")
            print(f"   - OCR去重后检测数: {len(all_text_results)}")

        else:
            print(f"🎯 图像尺寸适中，使用整图检测...")

            # 整图检测
            if hasattr(model, 'predict'):
                # MultiTaskModel 类型，使用predict方法
                try:
                    result = model.predict(image_path, save_result=False)
                    if 'component_detections' in result:
                        all_detections = result['component_detections']
                except Exception as e:
                    print(f"MultiTaskModel整图预测失败: {e}")

            elif hasattr(model, 'yolo_model'):
                # 如果模型有yolo_model属性，使用YOLO检测
                yolo_results = model.yolo_model.predict(
                    image_path,
                    conf=getattr(model, 'detection_conf_threshold', 0.15),
                    save=False,
                    verbose=False
                )

                # 提取YOLO检测结果
                if len(yolo_results) > 0:
                    detection_result = yolo_results[0]
                    if hasattr(detection_result, 'boxes') and detection_result.boxes is not None:
                        for box in detection_result.boxes:
                            x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                            confidence = float(box.conf[0])
                            class_id = int(box.cls[0])

                            detection = {
                                'class_id': class_id,
                                'confidence': confidence,
                                'bbox': [float(x1), float(y1), float(x2), float(y2)]
                            }
                            all_detections.append(detection)

            # 整图OCR检测
            if hasattr(model, 'predict'):
                # MultiTaskModel 类型，OCR结果已经在predict中获取
                try:
                    if 'text_recognition' in result:
                        all_text_results = result['text_recognition']
                except:
                    # 如果上面的predict失败，尝试单独的OCR检测
                    if hasattr(model, 'detect_text_with_all_ocr'):
                        all_text_results = model.detect_text_with_all_ocr(image)
                    else:
                        all_text_results = []
            elif hasattr(model, 'detect_text_with_all_ocr'):
                # MultiTaskModel 类型，但没有predict方法
                all_text_results = model.detect_text_with_all_ocr(image)
            elif hasattr(model, 'ocr_engines') and model.ocr_engines:
                # TrueIntegratedYOLOOCRModel 类型，直接使用OCR引擎
                try:
                    # 使用CnOCR
                    if 'cnocr' in model.ocr_engines:
                        from PIL import Image
                        # 转换为PIL图像
                        if len(image.shape) == 3:
                            image_pil = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
                        else:
                            image_pil = Image.fromarray(image)

                        cnocr_results = model.ocr_engines['cnocr'].ocr(image_pil)
                        for result in cnocr_results:
                            text = result.get('text', '')
                            confidence = result.get('score', 0.0)
                            if confidence > model.ocr_confidence_threshold and text.strip():
                                # 获取位置信息
                                position = result.get('position', [])
                                if len(position) >= 4:
                                    # 转换为bbox格式 [x1, y1, x2, y2]
                                    x_coords = [point[0] for point in position]
                                    y_coords = [point[1] for point in position]
                                    bbox = [
                                        int(min(x_coords)), int(min(y_coords)),
                                        int(max(x_coords)), int(max(y_coords))
                                    ]
                                    all_text_results.append({
                                        'text': text,
                                        'confidence': confidence,
                                        'detection_bbox': bbox,
                                        'engine': 'cnocr'
                                    })

                    # 使用EasyOCR
                    if 'easyocr' in model.ocr_engines:
                        easyocr_results = model.ocr_engines['easyocr'].readtext(image)
                        for bbox, text, confidence in easyocr_results:
                            if text.strip() and confidence > model.ocr_confidence_threshold:
                                # 转换bbox格式
                                x_coords = [point[0] for point in bbox]
                                y_coords = [point[1] for point in bbox]
                                x1, y1 = int(min(x_coords)), int(min(y_coords))
                                x2, y2 = int(max(x_coords)), int(max(y_coords))

                                all_text_results.append({
                                    'text': text,
                                    'confidence': confidence,
                                    'detection_bbox': [x1, y1, x2, y2],
                                    'engine': 'easyocr'
                                })

                except Exception as e:
                    print(f"整图OCR检测失败: {e}")
                    all_text_results = []

        print(f"✅ 统一滑动窗口检测完成:")
        print(f"   🎯 检测到目标: {len(all_detections)} 个")
        print(f"   📝 识别到文字: {len(all_text_results)} 段")

        # 保存结果
        if save_result:
            save_enhanced_results(all_detections, all_text_results, image_path, output_dir)

        return all_detections, all_text_results

    except Exception as e:
        print(f"❌ 统一滑动窗口检测错误: {e}")
        import traceback
        traceback.print_exc()
        return [], []

def detect_with_custom_model_enhanced(model, image_path, save_result=True, output_dir='results', use_yolo_sliding_window=True):
    """
    使用自定义模型进行增强检测（调用统一滑动窗口函数）
    """
    return detect_with_unified_sliding_window(
        model, image_path, save_result, output_dir,
        window_size=640, overlap_ratio=0.3, use_sliding_window=use_yolo_sliding_window
    )



def save_enhanced_results(detections, text_results, image_path, output_dir='results'):
    """
    保存增强检测结果，包括可视化图像
    """
    try:
        os.makedirs(output_dir, exist_ok=True)

        # 构建结果数据
        result_data = {
            'image_path': image_path,
            'timestamp': time.time(),
            'detection_count': len(detections),
            'text_count': len(text_results),
            'detections': detections,
            'text_recognition': text_results,
            'model_type': 'Enhanced_TrueIntegratedYOLOOCR'
        }

        # 保存JSON结果
        base_name = os.path.splitext(os.path.basename(image_path))[0]
        json_path = os.path.join(output_dir, f"{base_name}_enhanced_result.json")

        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(result_data, f, ensure_ascii=False, indent=2)

        print(f"💾 增强检测结果已保存: {json_path}")

        # 保存可视化图像
        visualize_enhanced_results(detections, text_results, image_path, output_dir)

    except Exception as e:
        print(f"❌ 保存增强结果失败: {e}")

def visualize_enhanced_results(detections, text_results, image_path, output_dir='results'):
    """
    可视化增强检测结果，包括目标检测框和文字识别框，支持中文显示
    """
    try:
        # 读取原图
        image = cv2.imread(image_path)
        if image is None:
            print(f"❌ 无法读取图像进行可视化: {image_path}")
            return

        print(f"🎨 开始可视化增强检测结果...")

        # 转换为PIL图像以支持中文字体
        from PIL import Image, ImageDraw, ImageFont

        # 转换BGR到RGB
        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        pil_image = Image.fromarray(image_rgb)
        draw = ImageDraw.Draw(pil_image)

        # 尝试加载中文字体
        try:
            # Windows系统字体路径
            font_path = "C:/Windows/Fonts/simhei.ttf"  # 黑体
            if not os.path.exists(font_path):
                font_path = "C:/Windows/Fonts/msyh.ttf"  # 微软雅黑
            if not os.path.exists(font_path):
                font_path = "C:/Windows/Fonts/simsun.ttc"  # 宋体

            font_small = ImageFont.truetype(font_path, 16)
            font_tiny = ImageFont.truetype(font_path, 12)
        except:
            # 如果无法加载字体，使用默认字体
            font_small = ImageFont.load_default()
            font_tiny = ImageFont.load_default()

        # 绘制目标检测框（绿色）
        detection_count = 0
        for detection in detections:
            bbox = detection['bbox']
            confidence = detection['confidence']
            class_id = detection.get('class_id', 0)

            x1, y1, x2, y2 = map(int, bbox)

            # 绘制检测框（绿色）
            draw.rectangle([x1, y1, x2, y2], outline=(0, 255, 0), width=2)

            # 添加标签
            label = f"目标:{class_id} {confidence:.2f}"

            # 绘制标签背景
            bbox_text = draw.textbbox((x1, y1-25), label, font=font_tiny)
            draw.rectangle(bbox_text, fill=(0, 255, 0))
            draw.text((x1, y1-25), label, font=font_tiny, fill=(0, 0, 0))

            detection_count += 1

        # 绘制文字识别框（蓝色）
        text_count = 0
        for text_result in text_results:
            if 'detection_bbox' in text_result or 'bbox' in text_result:
                # 获取边界框
                bbox = text_result.get('detection_bbox', text_result.get('bbox', []))
                if len(bbox) >= 4:
                    x1, y1, x2, y2 = map(int, bbox[:4])

                    # 绘制文字框（蓝色）
                    draw.rectangle([x1, y1, x2, y2], outline=(255, 0, 0), width=1)

                    # 添加文字标签
                    text = text_result.get('text', '')
                    confidence = text_result.get('confidence', 0.0)

                    # 限制文字长度
                    if len(text) > 8:
                        display_text = text[:8] + "..."
                    else:
                        display_text = text

                    label = f"{display_text} {confidence:.2f}"

                    # 绘制文字背景和标签
                    bbox_text = draw.textbbox((x1, y2), label, font=font_tiny)
                    draw.rectangle(bbox_text, fill=(255, 0, 0))
                    draw.text((x1, y2), label, font=font_tiny, fill=(255, 255, 255))

                    text_count += 1

        # 添加统计信息
        stats_text = [
            f"增强检测结果",
            f"目标检测: {detection_count} 个 (绿色框)",
            f"文字识别: {text_count} 个 (蓝色框)",
            f"总计对象: {detection_count + text_count} 个"
        ]

        # 绘制统计信息背景
        info_height = len(stats_text) * 30 + 20
        draw.rectangle([10, 10, 450, info_height], fill=(0, 0, 0))

        # 绘制统计文字
        for i, text in enumerate(stats_text):
            draw.text((15, 20 + i * 30), text, font=font_small, fill=(255, 255, 255))

        # 转换回OpenCV格式
        image_result = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)

        # 保存可视化结果
        base_name = os.path.splitext(os.path.basename(image_path))[0]
        output_path = os.path.join(output_dir, f"{base_name}_enhanced_visualization.jpg")

        cv2.imwrite(output_path, image_result)
        print(f"🖼️  增强可视化结果已保存: {output_path}")
        print(f"   📊 目标检测框: {detection_count} 个 (绿色)")
        print(f"   📝 文字识别框: {text_count} 个 (蓝色)")

        # 同时保存一些识别到的文字示例
        if text_results:
            print(f"📝 识别到的文字示例:")
            for i, text_result in enumerate(text_results[:10]):  # 显示前10个
                text = text_result.get('text', '')
                confidence = text_result.get('confidence', 0.0)
                engine = text_result.get('engine', 'unknown')
                print(f"   {i+1}. '{text}' (置信度: {confidence:.3f}, 引擎: {engine})")
            if len(text_results) > 10:
                print(f"   ... 还有 {len(text_results) - 10} 个文字结果")

    except Exception as e:
        print(f"❌ 可视化增强结果失败: {e}")
        import traceback
        traceback.print_exc()

def detect_with_custom_model(model, image_path, save_result=True, output_dir='results'):
    """
    使用自定义模型进行检测（保持向后兼容）
    """
    return detect_with_custom_model_enhanced(model, image_path, save_result, output_dir, use_yolo_sliding_window=True)

def detect_with_yolo(image_path, model_path, conf_threshold=0.5, imgsz=640, use_sliding_window=True, overlap_ratio=0.2):
    """
    使用YOLO进行目标检测，支持滑动窗口避免漏检

    Args:
        image_path: 图像路径
        model_path: 模型路径
        conf_threshold: 置信度阈值
        imgsz: 推理尺寸
        use_sliding_window: 是否使用滑动窗口
        overlap_ratio: 滑动窗口重叠比例

    Returns:
        detections: 检测结果列表
        None: 不再返回原始results对象
    """
    try:
        from ultralytics import YOLO

        print(f"🔧 加载模型: {model_path}")

        # 检查模型文件
        if not os.path.exists(model_path):
            print(f"❌ 模型文件不存在: {model_path}")
            return [], None

        # 检查模型文件大小
        file_size = os.path.getsize(model_path) / (1024 * 1024)  # MB
        print(f"📁 模型文件大小: {file_size:.1f} MB")

        if file_size < 1:
            print(f"❌ 模型文件太小，可能已损坏: {file_size:.1f} MB")
            return [], None

        # 尝试加载模型，添加更详细的错误处理
        try:
            # 首先检查是否是自定义的多任务模型
            model_data = torch.load(model_path, map_location='cpu')
            if isinstance(model_data, dict) and 'model_type' in model_data:
                print(f"🔍 检测到自定义多任务模型: {model_data.get('model_type', 'Unknown')}")
                print(f"❌ 当前脚本不支持加载自定义多任务模型")
                print(f"💡 建议：使用标准YOLO模型或修改脚本以支持多任务模型")

                # 尝试使用官方预训练模型作为替代
                print(f"🔄 使用YOLOv8n预训练模型作为替代...")
                model = YOLO('yolov8n.pt')
                print(f"✅ 使用官方预训练模型成功")
            else:
                # 尝试作为标准YOLO模型加载
                model = YOLO(model_path)
                print(f"✅ 标准YOLO模型加载成功")

        except KeyError as e:
            print(f"❌ 模型文件格式错误，缺少关键字段: {e}")
            print(f"💡 这可能是一个损坏的模型文件或不兼容的格式")
            print(f"🔄 尝试使用官方预训练模型...")

            try:
                model = YOLO('yolov8n.pt')  # 这会自动下载官方模型
                print(f"✅ 使用官方预训练模型成功")
            except Exception as e2:
                print(f"❌ 官方模型也无法加载: {e2}")
                return [], None

        except Exception as e:
            print(f"❌ 模型加载失败: {e}")
            print(f"🔄 尝试使用官方预训练模型...")

            try:
                model = YOLO('yolov8n.pt')  # 这会自动下载官方模型
                print(f"✅ 使用官方预训练模型成功")
            except Exception as e2:
                print(f"❌ 官方模型也无法加载: {e2}")
                return [], None
        
        # 检查GPU内存
        gpu_memory = check_gpu_memory()
        
        # 根据GPU内存选择设备和推理尺寸
        if gpu_memory < 8:
            device = 'cpu'
            imgsz = min(imgsz, 640)  # CPU使用较小尺寸
            print(f"⚠️  GPU内存不足，使用CPU，推理尺寸: {imgsz}")
        else:
            device = '0'
            print(f"✅ 使用GPU，推理尺寸: {imgsz}")

        # 确保尺寸是32的倍数
        if imgsz % 32 != 0:
            imgsz = ((imgsz // 32) + 1) * 32
            print(f"📏 调整推理尺寸为32的倍数: {imgsz}")
        print(f"� 将使用原始图像尺寸进行推理（不进行任何缩放）")
        
        # 清理GPU缓存
        if torch.cuda.is_available():
            torch.cuda.empty_cache()

        # 读取原始图像信息
        with Image.open(image_path) as img:
            original_width, original_height = img.size

        print(f"🚀 开始检测...")
        print(f"   - 图像: {image_path}")
        print(f"   - 原始尺寸: {original_width} x {original_height}")
        print(f"   - 推理尺寸: {imgsz}")
        print(f"   - 设备: {device}")
        print(f"   - 置信度: {conf_threshold}")
        print(f"   - 滑动窗口: {'启用' if use_sliding_window else '禁用'}")

        all_detections = []

        # 判断是否使用滑动窗口
        if use_sliding_window and (original_width > imgsz or original_height > imgsz):
            print(f"📐 图像较大，使用滑动窗口检测...")

            # 创建滑动窗口
            windows = create_sliding_windows(original_width, original_height, imgsz, overlap_ratio)

            # 对每个窗口进行检测
            for i, (x1, y1, x2, y2) in enumerate(windows):
                print(f"🔍 处理窗口 {i+1}/{len(windows)}: ({x1}, {y1}, {x2}, {y2})")

                # 裁剪图像窗口
                with Image.open(image_path) as img:
                    window_img = img.crop((x1, y1, x2, y2))
                    temp_window_path = f"temp_window_{i}.jpg"
                    window_img.save(temp_window_path, quality=95)

                try:
                    # 对窗口进行检测
                    results = model.predict(
                        temp_window_path,
                        conf=conf_threshold,
                        imgsz=imgsz,
                        device=device,
                        verbose=False,
                        save=False,
                        show=False,
                        half=False,
                        max_det=1000
                    )

                    # 处理窗口检测结果
                    if len(results) > 0 and len(results[0].boxes) > 0:
                        boxes = results[0].boxes
                        for box in boxes:
                            box_x1, box_y1, box_x2, box_y2 = box.xyxy[0].cpu().numpy()
                            confidence = float(box.conf[0])
                            class_id = int(box.cls[0])

                            # 转换坐标到原图坐标系
                            global_x1 = x1 + box_x1
                            global_y1 = y1 + box_y1
                            global_x2 = x1 + box_x2
                            global_y2 = y1 + box_y2

                            detection = {
                                'class_id': class_id,
                                'confidence': confidence,
                                'bbox': [float(global_x1), float(global_y1), float(global_x2), float(global_y2)]
                            }
                            all_detections.append(detection)

                finally:
                    # 清理临时文件
                    if os.path.exists(temp_window_path):
                        os.remove(temp_window_path)

            # 对所有检测结果进行NMS去重
            print(f"🔄 检测完成，进行NMS去重...")
            print(f"   - 原始检测数: {len(all_detections)}")
            all_detections = non_max_suppression_custom(all_detections, iou_threshold=0.5)
            print(f"   - 去重后检测数: {len(all_detections)}")

        else:
            print(f"📐 图像尺寸适中或禁用滑动窗口，使用整图检测...")

            # 预处理图像（根据训练尺寸调整）
            processed_path, scale_factor = preprocess_image(image_path, target_size=imgsz)

            # 进行检测（使用训练兼容的推理尺寸）
            results = model.predict(
                processed_path,
                conf=conf_threshold,
                imgsz=imgsz,
                device=device,
                verbose=False,
                save=False,
                show=False,
                half=False,
                max_det=1000
            )

            # 处理结果
            if len(results) > 0 and len(results[0].boxes) > 0:
                boxes = results[0].boxes
                for box in boxes:
                    x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                    confidence = float(box.conf[0])
                    class_id = int(box.cls[0])

                    # 如果图像被缩放，需要还原到原始坐标
                    if scale_factor != 1.0:
                        x1, y1, x2, y2 = x1/scale_factor, y1/scale_factor, x2/scale_factor, y2/scale_factor

                    detection = {
                        'class_id': class_id,
                        'confidence': confidence,
                        'bbox': [float(x1), float(y1), float(x2), float(y2)]
                    }
                    all_detections.append(detection)

            # 清理临时文件
            if 'processed_path' in locals() and processed_path != image_path and os.path.exists(processed_path):
                os.remove(processed_path)
                print(f"🗑️  已清理临时文件: {processed_path}")
        
        print(f"✅ 检测完成!")

        # 显示最终结果
        if all_detections:
            print(f"📦 最终检测到 {len(all_detections)} 个目标:")
            for i, detection in enumerate(all_detections):
                bbox = detection['bbox']
                class_id = detection['class_id']
                print(f"  {i+1}. 类别ID: {class_id}, "
                      f"位置: ({bbox[0]:.1f}, {bbox[1]:.1f}, {bbox[2]:.1f}, {bbox[3]:.1f})")
        else:
            print("❌ 未检测到任何目标")

        return all_detections, None
        
    except Exception as e:
        print(f"❌ 检测错误: {e}")
        import traceback
        traceback.print_exc()
        return [], None

def save_results(detections, image_path, output_path="detection_results.json"):
    """保存检测结果"""
    result = {
        'image_path': image_path,
        'timestamp': time.time(),
        'detections': detections,
        'total_detections': len(detections)
    }
    
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(result, f, ensure_ascii=False, indent=2)
    
    print(f"💾 结果已保存到: {output_path}")

def visualize_results(image_path, detections, output_path="detection_result.jpg"):
    """可视化检测结果"""
    try:
        import cv2
        
        # 读取图像
        image = cv2.imread(image_path)
        if image is None:
            print(f"❌ 无法读取图像: {image_path}")
            return
        
        # 绘制检测框
        for detection in detections:
            bbox = detection['bbox']
            class_id = detection['class_id']

            x1, y1, x2, y2 = map(int, bbox)
            
            # 绘制边界框
            cv2.rectangle(image, (x1, y1), (x2, y2), (0, 255, 0), 2)
            
            # 绘制标签
            label = f"ID:{class_id}"
            label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]
            cv2.rectangle(image, (x1, y1 - label_size[1] - 10), 
                         (x1 + label_size[0], y1), (0, 255, 0), -1)
            cv2.putText(image, label, (x1, y1 - 5), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 2)
        
        # 保存结果图像
        cv2.imwrite(output_path, image)
        print(f"🖼️  可视化结果已保存到: {output_path}")
        
    except Exception as e:
        print(f"❌ 可视化错误: {e}")

def main():
    """主函数"""
    import argparse

    # 解析命令行参数
    parser = argparse.ArgumentParser(description='直接检测脚本 - 支持自定义模型加载')
    parser.add_argument('--image', type=str, default='DaYuanTuZ_0.png',
                       help='输入图像路径 (默认: DaYuanTuZ_0.png)')
    parser.add_argument('--model', type=str, default='models/integrated_yolo_ocr_model.pt',
                       help='模型文件路径 (默认: models/integrated_yolo_ocr_model.pt)')
    parser.add_argument('--conf', type=float, default=0.4,
                       help='置信度阈值 (默认: 0.4)')
    parser.add_argument('--imgsz', type=int, default=640,
                       help='推理图像尺寸 (默认: 640)')
    parser.add_argument('--sliding-window', action='store_true', default=True,
                       help='启用滑动窗口检测 (默认: True)')
    parser.add_argument('--overlap', type=float, default=0.2,
                       help='滑动窗口重叠比例 (默认: 0.2)')
    parser.add_argument('--output-dir', type=str, default='results',
                       help='输出目录 (默认: results)')

    args = parser.parse_args()

    print("🎯 开始直接检测...")
    print(f"📋 配置参数:")
    print(f"   📸 图像文件: {args.image}")
    print(f"   🤖 模型文件: {args.model}")
    print(f"   🎯 置信度阈值: {args.conf}")
    print(f"   📐 推理尺寸: {args.imgsz}")
    print(f"   🔲 滑动窗口: {'启用' if args.sliding_window else '禁用'}")
    print(f"   📊 重叠比例: {args.overlap}")
    print(f"   📁 输出目录: {args.output_dir}")

    # 配置参数
    image_path = args.image
    model_path = args.model
    conf_threshold = args.conf
    imgsz = args.imgsz
    use_sliding_window = args.sliding_window
    overlap_ratio = args.overlap
    output_dir = args.output_dir

    # 检查文件是否存在
    if not os.path.exists(image_path):
        print(f"❌ 图像文件不存在: {image_path}")
        return

    print(f"📁 图像文件: {image_path}")
    print(f"🤖 模型文件: {model_path}")

    # 首先尝试加载自定义模型
    custom_model = None
    if os.path.exists(model_path) and CUSTOM_MODEL_AVAILABLE:
        print(f"🔧 尝试加载自定义多任务模型...")
        custom_model = load_custom_model(model_path)

    if custom_model is not None:
        print(f"✅ 使用自定义多任务模型进行检测")
        print(f"🔧 模型特性:")
        print(f"   - 同时支持目标检测和文字识别")
        print(f"   - 集成多个OCR引擎")
        print(f"   - 自动优化检测参数")

        # 使用自定义模型进行检测
        detections, text_results = detect_with_custom_model(
            custom_model, image_path, save_result=True, output_dir=output_dir
        )

        # 显示结果
        if detections or text_results:
            print(f"\n🎉 检测完成!")
            print(f"   🎯 目标检测: {len(detections)} 个")
            print(f"   📝 文字识别: {len(text_results)} 段")

            # 显示文字识别结果
            if text_results:
                print(f"\n� 识别到的文字:")
                for i, text_result in enumerate(text_results[:10]):  # 只显示前10个
                    text = text_result.get('text', '')
                    confidence = text_result.get('confidence', 0.0)
                    print(f"   {i+1}. '{text}' (置信度: {confidence:.3f})")
                if len(text_results) > 10:
                    print(f"   ... 还有 {len(text_results) - 10} 个文字结果")
        else:
            print(f"\n❌ 未检测到任何目标或文字")

    else:
        print(f"⚠️  无法加载自定义模型，使用标准YOLO检测")
        print(f"🔧 检测配置:")
        print(f"   - 置信度阈值: {conf_threshold}")
        print(f"   - 推理尺寸: {imgsz}")
        print(f"   - 滑动窗口: {'启用' if use_sliding_window else '禁用'}")
        if use_sliding_window:
            print(f"   - 重叠比例: {overlap_ratio:.1%}")

        # 使用标准YOLO检测
        detections, _ = detect_with_yolo(
            image_path, model_path, conf_threshold, imgsz, use_sliding_window, overlap_ratio
        )

        # 保存结果
        if detections:
            save_results(detections, image_path)
            visualize_results(image_path, detections)
            print(f"\n🎉 检测完成! 共检测到 {len(detections)} 个目标")
        else:
            print(f"\n❌ 未检测到任何目标")

if __name__ == "__main__":
    main()
